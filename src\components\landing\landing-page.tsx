'use client';

import { useRouter } from '@/i18n/navigation';
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Container from '@mui/material/Container';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import useMediaQuery from '@mui/material/useMediaQuery';
import { Brain, ChartLineUp, GithubLogo, Lightning, Plugs, Shield } from '@phosphor-icons/react/dist/ssr';
import { useLocale, useTranslations } from 'next-intl';
import * as React from 'react';

import { DynamicLogo } from '@/components/core/logo';
import { useAuth } from '@/contexts/firebase-auth-context';
import { paths } from '@/paths';

// Import react-flagkit for flag components
const Flag = require('react-flagkit').default;

import { HeroCarousel } from './hero-carousel';
import { LeadCaptureModal } from './lead-capture-modal';

export function LandingPage(): React.JSX.Element {
  const router = useRouter();
  const { loading } = useAuth();
  const t = useTranslations('landing');
  const locale = useLocale();

  // Responsive breakpoints for performance optimization
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  // const isDesktop = useMediaQuery(theme.breakpoints.up('lg')); // Unused for now

  // Mobile menu state
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  // Language selector functionality - mapping locale to country code for react-flagkit
  const localeToCountryCode: Record<string, string> = {
    'en-US': 'US',
    'pt-BR': 'BR',
    es: 'ES',
  };

  const handleLanguageChange = (newLocale: string | null) => {
    if (newLocale && newLocale !== locale) {
      router.replace(
        // @ts-expect-error -- TypeScript will validate that only known `params`
        // are used in combination with a given `pathname`. Since the two will
        // always match for the current route, we can skip runtime checks.
        { pathname: window.location.pathname },
        { locale: newLocale as any }
      );
      router.refresh();
    }
  };

  // Get language name from translation
  const getLanguageName = (localeStr: string): string => {
    return t('languageSwitch', { locale: localeStr.replaceAll('-', '_') });
  };

  // Import routing for locale options
  const { routing } = require('@/i18n/routing');

  // Performance optimization: Memoize expensive calculations
  const heroFontSize = React.useMemo(() => {
    if (isMobile) return '2rem';
    if (isTablet) return '3rem';
    return '4.5rem';
  }, [isMobile, isTablet]);

  const sectionPadding = React.useMemo(() => {
    return { xs: 0, md: 0 }; // Removed padding for seamless background continuity
  }, []);

  const handleBookDemo = React.useCallback(() => {
    // Always redirect to sign in for demo booking, regardless of auth status
    router.push(paths.auth.signIn);
  }, [router]);

  // Performance optimization: Memoize navigation handlers
  const handleMobileMenuToggle = React.useCallback(() => {
    setMobileMenuOpen(!mobileMenuOpen);
  }, [mobileMenuOpen]);

  const handleMobileMenuClose = React.useCallback(() => {
    setMobileMenuOpen(false);
  }, []);

  // Lead capture modal state
  const [leadModalOpen, setLeadModalOpen] = React.useState(false);

  const handleLeadModalOpen = React.useCallback(() => {
    setLeadModalOpen(true);
  }, []);

  const handleLeadModalClose = React.useCallback(() => {
    setLeadModalOpen(false);
  }, []);

  // Smooth scroll to sections
  const scrollToSection = React.useCallback((sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
    setMobileMenuOpen(false);
  }, []);

  // Performance optimization: Intersection Observer for animations (removed for now)

  // Performance optimization: Preload critical resources
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // Preload critical CSS for faster rendering
      const preloadCSS = () => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = '/fonts/inter.css';
        document.head.appendChild(link);
      };

      // Optimize images with lazy loading
      const images = document.querySelectorAll('img[data-src]');
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              img.src = img.dataset.src || '';
              img.classList.remove('lazy');
              imageObserver.unobserve(img);
            }
          });
        });
        images.forEach((img) => imageObserver.observe(img));
      }

      preloadCSS();

      // Performance monitoring
      if ('performance' in window && 'PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              if (entry.entryType === 'largest-contentful-paint') {
                console.log('LCP:', entry.startTime);
              }
            });
          });
          observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (error) {
          // Silently fail in test environment
          console.warn('Performance monitoring not available:', error);
        }
      }
    }
  }, []);

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          bgcolor: 'primary.dark',
        }}
      >
        <DynamicLogo height={60} width={60} />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        background: (muiTheme) =>
          `linear-gradient(135deg, ${muiTheme.palette.secondary.dark} 0%, ${muiTheme.palette.secondary.main} 50%, ${muiTheme.palette.secondary.dark} 100%)`,
        color: 'white',
      }}
    >
      {/* Header */}
      <Box
        component='header'
        sx={{
          py: 3,
          px: { xs: 2, md: 4 },
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <DynamicLogo height={40} width={40} />
          <Typography
            variant='h5'
            sx={{
              fontWeight: 700,
              color: 'white',
              letterSpacing: '-0.02em',
            }}
          >
            BMS Pulse
          </Typography>
        </Box>
        {/* Mobile Menu Button */}
        <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
          <Button
            variant='text'
            onClick={handleMobileMenuToggle}
            sx={{
              color: 'white',
              minWidth: 'auto',
              p: 1,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 0.5,
                '& > div': {
                  width: 24,
                  height: 2,
                  bgcolor: 'white',
                  borderRadius: 1,
                  transition: 'all 0.3s ease',
                  transformOrigin: 'center',
                },
                ...(mobileMenuOpen && {
                  '& > div:nth-of-type(1)': {
                    transform: 'rotate(45deg) translate(6px, 6px)',
                  },
                  '& > div:nth-of-type(2)': {
                    opacity: 0,
                  },
                  '& > div:nth-of-type(3)': {
                    transform: 'rotate(-45deg) translate(6px, -6px)',
                  },
                }),
              }}
            >
              <Box />
              <Box />
              <Box />
            </Box>
          </Button>
        </Box>

        {/* Desktop Navigation */}
        <Stack direction='row' spacing={4} sx={{ display: { xs: 'none', md: 'flex' } }}>
          <Button
            variant='text'
            onClick={() => scrollToSection('features')}
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              '&:hover': {
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            {t('header.features')}
          </Button>
          <Button
            variant='text'
            onClick={() => scrollToSection('pricing')}
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              '&:hover': {
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            {t('header.pricing')}
          </Button>
          <Button
            variant='text'
            onClick={() => scrollToSection('integrations')}
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              '&:hover': {
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            {t('header.integrations')}
          </Button>

          {/* Language Selector */}
          <Autocomplete
            value={locale}
            onChange={(_, newValue) => handleLanguageChange(newValue)}
            options={routing.locales}
            getOptionLabel={(option) => ''}
            renderOption={(props, option) => (
              <ListItem {...props} key={option}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Flag country={localeToCountryCode[option]} size={20} />
                  <ListItemText primary={getLanguageName(option)} />
                </Box>
              </ListItem>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                variant='outlined'
                size='small'
                sx={{
                  minWidth: 60,
                  '& .MuiOutlinedInput-root': {
                    color: 'white',
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.3)',
                    },
                    '&:hover fieldset': {
                      borderColor: 'rgba(255, 255, 255, 0.5)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: 'primary.main',
                    },
                  },
                  '& .MuiInputBase-input': {
                    color: 'white',
                    fontSize: '1.25rem',
                    textAlign: 'center',
                    padding: '8px 12px !important',
                  },
                  '& .MuiSvgIcon-root': {
                    color: 'rgba(255, 255, 255, 0.7)',
                  },
                }}
                InputProps={{
                  ...params.InputProps,
                  startAdornment: (
                    <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                      <Flag country={localeToCountryCode[locale]} size={20} />
                    </Box>
                  ),
                }}
                inputProps={{
                  ...params.inputProps,
                  value: '',
                }}
              />
            )}
            sx={{ minWidth: 60 }}
          />

          <Button
            variant='contained'
            onClick={handleLeadModalOpen}
            sx={{
              bgcolor: 'primary.main',
              color: 'white',
              borderRadius: 3,
              textTransform: 'none',
              fontWeight: 600,
              fontSize: '1rem',
              px: 4,
              py: 1.5,
              boxShadow: '0 8px 32px rgba(33, 150, 243, 0.3)',
              '&:hover': {
                bgcolor: 'primary.dark',
                boxShadow: '0 12px 40px rgba(33, 150, 243, 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            {t('hero.ctaSecondary')}
          </Button>
        </Stack>
      </Box>

      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: '#0D1117', // Using Midnight + Electric-Blue design system primary background
            backdropFilter: 'blur(20px)',
            zIndex: 1000,
            display: { xs: 'flex', md: 'none' },
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            animation: 'fadeIn 0.3s ease-out',
            '@keyframes fadeIn': {
              from: { opacity: 0 },
              to: { opacity: 1 },
            },
          }}
        >
          {/* Close Button */}
          <Box
            sx={{
              position: 'absolute',
              top: 24,
              right: 24,
              zIndex: 1001,
            }}
          >
            <Button
              variant='text'
              onClick={handleMobileMenuClose}
              sx={{
                color: '#94A3B8', // Muted text color from design system
                minWidth: 'auto',
                p: 1,
                borderRadius: 2,
                '&:hover': {
                  color: '#F8FAFC', // Primary text color on hover
                  bgcolor: '#161B22', // Surface background color
                },
                transition: 'all 0.3s ease',
              }}
            >
              <Box
                component='svg'
                sx={{
                  width: 24,
                  height: 24,
                  fill: 'currentColor',
                }}
                viewBox='0 0 256 256'
              >
                <path d='M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z'></path>
              </Box>
            </Button>
          </Box>
          <Stack spacing={4} alignItems='center'>
            <Button
              variant='text'
              onClick={() => scrollToSection('features')}
              sx={{
                color: '#F8FAFC', // Primary text color from design system
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1.25rem',
                py: 2,
                px: 3,
                borderRadius: 2,
                '&:hover': {
                  color: '#3B82F6', // Electric-Blue accent color
                  bgcolor: '#161B22', // Surface background color
                },
                transition: 'all 0.3s ease',
              }}
            >
              {t('header.features')}
            </Button>
            <Button
              variant='text'
              onClick={() => scrollToSection('pricing')}
              sx={{
                color: '#F8FAFC', // Primary text color from design system
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1.25rem',
                py: 2,
                px: 3,
                borderRadius: 2,
                '&:hover': {
                  color: '#3B82F6', // Electric-Blue accent color
                  bgcolor: '#161B22', // Surface background color
                },
                transition: 'all 0.3s ease',
              }}
            >
              {t('header.pricing')}
            </Button>
            <Button
              variant='text'
              onClick={() => scrollToSection('integrations')}
              sx={{
                color: '#F8FAFC', // Primary text color from design system
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1.25rem',
                py: 2,
                px: 3,
                borderRadius: 2,
                '&:hover': {
                  color: '#3B82F6', // Electric-Blue accent color
                  bgcolor: '#161B22', // Surface background color
                },
                transition: 'all 0.3s ease',
              }}
            >
              {t('header.integrations')}
            </Button>

            {/* Mobile Language Selector */}
            <Autocomplete
              value={locale}
              onChange={(_, newValue) => handleLanguageChange(newValue)}
              options={routing.locales}
              getOptionLabel={(option) => ''}
              renderOption={(props, option) => (
                <ListItem {...props} key={option}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Flag country={localeToCountryCode[option]} size={20} />
                    <ListItemText primary={getLanguageName(option)} />
                  </Box>
                </ListItem>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant='outlined'
                  size='small'
                  sx={{
                    minWidth: 80,
                    '& .MuiOutlinedInput-root': {
                      color: '#F8FAFC', // Primary text color from design system
                      borderRadius: 2,
                      bgcolor: '#161B22', // Surface background color
                      '& fieldset': {
                        borderColor: '#30363D', // Subtle border color from design system
                      },
                      '&:hover fieldset': {
                        borderColor: '#3B82F6', // Electric-Blue accent color
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#3B82F6', // Electric-Blue accent color
                      },
                    },
                    '& .MuiInputBase-input': {
                      color: '#F8FAFC', // Primary text color from design system
                      fontSize: '1.5rem',
                      textAlign: 'center',
                      padding: '12px 16px !important',
                    },
                    '& .MuiSvgIcon-root': {
                      color: '#94A3B8', // Muted text color from design system
                    },
                  }}
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: (
                      <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                        <Flag country={localeToCountryCode[locale]} size={24} />
                      </Box>
                    ),
                  }}
                  inputProps={{
                    ...params.inputProps,
                    value: '',
                  }}
                />
              )}
              sx={{ minWidth: 80, mt: 2 }}
            />

            <Button
              variant='contained'
              onClick={() => {
                handleMobileMenuClose();
                handleLeadModalOpen();
              }}
              sx={{
                bgcolor: '#3B82F6', // Electric-Blue accent color from design system
                color: '#F8FAFC', // Primary text color from design system
                borderRadius: 3,
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1.25rem',
                px: 6,
                py: 2,
                mt: 2,
                boxShadow: '0 8px 32px rgba(59, 130, 246, 0.3)', // Electric-Blue shadow
                '&:hover': {
                  bgcolor: '#2563EB', // Darker Electric-Blue for hover
                  boxShadow: '0 12px 40px rgba(59, 130, 246, 0.4)',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease',
              }}
            >
              {t('hero.ctaSecondary')}
            </Button>
          </Stack>
        </Box>
      )}

      {/* Hero Carousel Section */}
      <HeroCarousel onLeadModalOpen={handleLeadModalOpen} />

      {/* Features Section */}
      <Box id='features' sx={{ py: sectionPadding, bgcolor: 'rgba(255, 255, 255, 0.02)' }}>
        <Container maxWidth='xl' sx={{ py: { xs: 4, md: 8 } }}>
          <Box sx={{ textAlign: 'center', mb: { xs: 4, md: 6 } }}>
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '2rem', md: '3rem' },
                fontWeight: 700,
                color: 'white',
                mb: 3,
                letterSpacing: '-0.02em',
              }}
            >
              {t('features.title')}
            </Typography>
            <Typography
              variant='h6'
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                maxWidth: '600px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', md: '1.25rem' },
                lineHeight: 1.6,
              }}
            >
              {t('features.subtitle')}
            </Typography>
          </Box>

          {/* Features Grid */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
              gap: 4,
            }}
          >
            {/* AI Agents Feature */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 3,
                      bgcolor: 'rgba(33, 150, 243, 0.2)',
                      border: '1px solid rgba(33, 150, 243, 0.3)',
                    }}
                  >
                    <Brain size={32} color='#2196f3' />
                  </Box>
                  <Typography
                    variant='h5'
                    sx={{ color: 'white', fontWeight: 700, fontSize: { xs: '1.25rem', md: '1.5rem' } }}
                  >
                    {t('features.aiAgents.title')}
                  </Typography>
                </Box>
                <Typography
                  variant='body1'
                  sx={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: { xs: '1rem', md: '1.1rem' },
                    lineHeight: 1.7,
                  }}
                >
                  {t('features.aiAgents.description')}
                </Typography>
              </CardContent>
            </Card>

            {/* Integrations Feature */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 3,
                      bgcolor: 'rgba(33, 150, 243, 0.2)',
                      border: '1px solid rgba(33, 150, 243, 0.3)',
                    }}
                  >
                    <Plugs size={32} color='#2196f3' />
                  </Box>
                  <Typography
                    variant='h5'
                    sx={{ color: 'white', fontWeight: 700, fontSize: { xs: '1.25rem', md: '1.5rem' } }}
                  >
                    {t('features.integrations.title')}
                  </Typography>
                </Box>
                <Typography
                  variant='body1'
                  sx={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: { xs: '1rem', md: '1.1rem' },
                    lineHeight: 1.7,
                  }}
                >
                  {t('features.integrations.description')}
                </Typography>
              </CardContent>
            </Card>

            {/* Insights Feature */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 3,
                      bgcolor: 'rgba(33, 150, 243, 0.2)',
                      border: '1px solid rgba(33, 150, 243, 0.3)',
                    }}
                  >
                    <ChartLineUp size={32} color='#2196f3' />
                  </Box>
                  <Typography
                    variant='h5'
                    sx={{ color: 'white', fontWeight: 700, fontSize: { xs: '1.25rem', md: '1.5rem' } }}
                  >
                    {t('features.insights.title')}
                  </Typography>
                </Box>
                <Typography
                  variant='body1'
                  sx={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: { xs: '1rem', md: '1.1rem' },
                    lineHeight: 1.7,
                  }}
                >
                  {t('features.insights.description')}
                </Typography>
              </CardContent>
            </Card>

            {/* Security Feature */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 3,
                      bgcolor: 'rgba(33, 150, 243, 0.2)',
                      border: '1px solid rgba(33, 150, 243, 0.3)',
                    }}
                  >
                    <Shield size={32} color='#2196f3' />
                  </Box>
                  <Typography
                    variant='h5'
                    sx={{ color: 'white', fontWeight: 700, fontSize: { xs: '1.25rem', md: '1.5rem' } }}
                  >
                    {t('features.security.title')}
                  </Typography>
                </Box>
                <Typography
                  variant='body1'
                  sx={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: { xs: '1rem', md: '1.1rem' },
                    lineHeight: 1.7,
                  }}
                >
                  {t('features.security.description')}
                </Typography>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Box>

      {/* Integration Showcase Section */}
      <Box id='integrations' sx={{ py: sectionPadding, bgcolor: 'rgba(255, 255, 255, 0.02)' }}>
        <Container maxWidth='xl' sx={{ py: { xs: 4, md: 8 } }}>
          {/* Section Header */}
          <Box sx={{ textAlign: 'center', mb: { xs: 4, md: 6 } }}>
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '2rem', md: '3rem' },
                fontWeight: 700,
                color: 'white',
                mb: 3,
                letterSpacing: '-0.02em',
              }}
            >
              {t('integrations.title')}
            </Typography>
            <Typography
              variant='h6'
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', md: '1.25rem' },
                lineHeight: 1.6,
              }}
            >
              {t('integrations.subtitle')}
            </Typography>
          </Box>

          {/* Main Integration Categories */}
          <Stack spacing={6}>
            {/* Development & Version Control */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                position: 'relative',
                overflow: 'hidden',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: 4,
                  background: 'linear-gradient(90deg, #2196f3 0%, #21cbf3 50%, #2196f3 100%)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 4, md: 6 } }}>
                <Box sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Typography variant='h4' sx={{ color: 'white', fontWeight: 700 }}>
                      {t('integrations.categories.development.title')}
                    </Typography>
                  </Box>
                  <Typography variant='body1' sx={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '1.1rem' }}>
                    {t('integrations.categories.development.description')}
                  </Typography>
                </Box>
                <Stack direction={{ xs: 'column', md: 'row' }} spacing={4}>
                  {/* GitHub Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <GithubLogo size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        GitHub
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Pull requests, commits, issues, and repository analytics
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Real-time sync
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Webhooks
                      </Box>
                    </Box>
                  </Box>

                  {/* GitLab Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <GithubLogo size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        GitLab
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Merge requests, pipelines, and deployment tracking
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        CI/CD metrics
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        API integration
                      </Box>
                    </Box>
                  </Box>

                  {/* Bitbucket Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Shield size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Bitbucket
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Repository management and team collaboration
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Team insights
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Branch analysis
                      </Box>
                    </Box>
                  </Box>
                </Stack>
              </CardContent>
            </Card>

            {/* Project Management */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 4, md: 6 } }}>
                <Box sx={{ mb: 4 }}>
                  <Typography variant='h4' sx={{ color: 'white', fontWeight: 700, mb: 2 }}>
                    {t('integrations.categories.projectManagement.title')}
                  </Typography>
                  <Typography variant='body1' sx={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '1.1rem' }}>
                    {t('integrations.categories.projectManagement.description')}
                  </Typography>
                </Box>
                <Stack direction={{ xs: 'column', md: 'row' }} spacing={4}>
                  {/* Jira Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Plugs size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Jira
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Sprint planning, issue tracking, and velocity metrics
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Sprint analytics
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Burndown charts
                      </Box>
                    </Box>
                  </Box>

                  {/* Azure DevOps Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Lightning size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Azure DevOps
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Work items, boards, and release management
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Release tracking
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Work item flow
                      </Box>
                    </Box>
                  </Box>

                  {/* Linear Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <ChartLineUp size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Linear
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Modern issue tracking and project planning
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Cycle analytics
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Team velocity
                      </Box>
                    </Box>
                  </Box>
                </Stack>
              </CardContent>
            </Card>

            {/* Communication & Collaboration */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 4, md: 6 } }}>
                <Box sx={{ mb: 4 }}>
                  <Typography variant='h4' sx={{ color: 'white', fontWeight: 700, mb: 2 }}>
                    {t('integrations.categories.communication.title')}
                  </Typography>
                  <Typography variant='body1' sx={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '1.1rem' }}>
                    {t('integrations.categories.communication.description')}
                  </Typography>
                </Box>
                <Stack direction={{ xs: 'column', md: 'row' }} spacing={4}>
                  {/* Slack Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Brain size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Slack
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      {t('integrations.tools.slack.description')}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Smart alerts
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Custom reports
                      </Box>
                    </Box>
                  </Box>

                  {/* Microsoft Teams Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Shield size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Microsoft Teams
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      {t('integrations.tools.teams.description')}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Meeting analytics
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Team insights
                      </Box>
                    </Box>
                  </Box>

                  {/* Discord Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Brain size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Discord
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      {t('integrations.tools.discord.description')}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Community insights
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Activity tracking
                      </Box>
                    </Box>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Stack>

          {/* Integration Benefits */}
          <Box sx={{ mt: { xs: 8, md: 12 }, textAlign: 'center' }}>
            <Typography variant='h4' sx={{ color: 'white', fontWeight: 700, mb: 4 }}>
              {t('integrations.benefits.title')}
            </Typography>
            <Stack direction={{ xs: 'column', md: 'row' }} spacing={4} justifyContent='center'>
              <Box sx={{ textAlign: 'center' }}>
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'rgba(33, 150, 243, 0.2)',
                    border: '2px solid rgba(33, 150, 243, 0.4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2,
                  }}
                >
                  <Lightning size={40} color='#2196f3' />
                </Box>
                <Typography variant='h6' sx={{ color: 'white', fontWeight: 600, mb: 1 }}>
                  {t('integrations.benefits.oneClick.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  {t('integrations.benefits.oneClick.description')}
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'rgba(33, 150, 243, 0.2)',
                    border: '2px solid rgba(33, 150, 243, 0.4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2,
                  }}
                >
                  <Shield size={40} color='#2196f3' />
                </Box>
                <Typography variant='h6' sx={{ color: 'white', fontWeight: 600, mb: 1 }}>
                  {t('integrations.benefits.security.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  {t('integrations.benefits.security.description')}
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'rgba(33, 150, 243, 0.2)',
                    border: '2px solid rgba(33, 150, 243, 0.4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2,
                  }}
                >
                  <ChartLineUp size={40} color='#2196f3' />
                </Box>
                <Typography variant='h6' sx={{ color: 'white', fontWeight: 600, mb: 1 }}>
                  {t('integrations.benefits.realTime.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  {t('integrations.benefits.realTime.description')}
                </Typography>
              </Box>
            </Stack>
          </Box>
        </Container>
      </Box>

      {/* Pricing Section */}
      <Box id='pricing' sx={{ py: sectionPadding, bgcolor: 'rgba(255, 255, 255, 0.02)' }}>
        <Container maxWidth='xl' sx={{ py: { xs: 4, md: 8 } }}>
          {/* Section Header */}
          <Box sx={{ textAlign: 'center', mb: { xs: 4, md: 6 } }}>
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '2rem', md: '3rem' },
                fontWeight: 700,
                color: 'white',
                mb: 3,
                letterSpacing: '-0.02em',
              }}
            >
              {t('pricing.title')}
            </Typography>
            <Typography
              variant='h6'
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                maxWidth: '600px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', md: '1.25rem' },
                lineHeight: 1.6,
              }}
            >
              {t('pricing.subtitle')}
            </Typography>
          </Box>

          {/* Pricing Cards */}
          <Stack
            direction={{ xs: 'column', lg: 'row' }}
            spacing={4}
            justifyContent='center'
            sx={{
              pt: 4, // Increased padding to provide space for the badge
              overflow: 'visible', // Ensure badges are not clipped
              position: 'relative', // Establish stacking context
            }}
          >
            {/* Team Manager Plan */}
            <Card
              sx={{
                maxWidth: 400,
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Typography variant='h5' sx={{ color: 'white', fontWeight: 700, mb: 1 }}>
                  {t('pricing.manager.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 4 }}>
                  {t('pricing.manager.description')}
                </Typography>
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant='h3'
                    sx={{
                      color: 'primary.light',
                      fontWeight: 800,
                      display: 'inline',
                    }}
                  >
                    {t('pricing.manager.price')}
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'rgba(255, 255, 255, 0.7)',
                      display: 'inline',
                      ml: 1,
                    }}
                  >
                    {t('pricing.manager.period')}
                  </Typography>
                </Box>
                <Stack spacing={2} sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Team performance metrics
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Sprint analytics
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Developer productivity
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Integration management
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Standard support
                    </Typography>
                  </Box>
                </Stack>
                <Button
                  variant='outlined'
                  fullWidth
                  onClick={handleBookDemo}
                  sx={{
                    py: 2,
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: '1rem',
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    color: 'white',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: 'rgba(33, 150, 243, 0.1)',
                    },
                  }}
                >
                  {t('pricing.buttons.getStarted')}
                </Button>
              </CardContent>
            </Card>

            {/* Executive Suite Plan - Featured */}
            <Card
              sx={{
                maxWidth: 400,
                bgcolor: 'rgba(33, 150, 243, 0.1)',
                border: '2px solid',
                borderColor: 'primary.main',
                backdropFilter: 'blur(10px)',
                overflow: 'visible',
                position: 'relative',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(33, 150, 243, 0.4)',
                },
              }}
            >
              {/* Popular Badge */}
              <Box
                sx={{
                  position: 'absolute',
                  top: -18, // Slightly higher for better visibility
                  left: '50%',
                  transform: 'translateX(-50%)',
                  bgcolor: 'primary.main',
                  color: 'white',
                  px: 3,
                  py: 1,
                  borderRadius: 2,
                  fontSize: '0.875rem',
                  fontWeight: 600,
                  zIndex: 20, // Higher z-index for better stacking
                  boxShadow: '0 6px 16px rgba(33, 150, 243, 0.4)', // Enhanced shadow
                  whiteSpace: 'nowrap', // Prevent text wrapping
                  border: '1px solid rgba(33, 150, 243, 0.5)', // Subtle border for definition
                }}
              >
                {t('pricing.mostPopular')}
              </Box>
              <CardContent sx={{ p: 4, pt: 5 }}>
                <Typography variant='h5' sx={{ color: 'white', fontWeight: 700, mb: 1 }}>
                  {t('pricing.cto.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 4 }}>
                  {t('pricing.cto.description')}
                </Typography>
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant='h3'
                    sx={{
                      color: 'primary.light',
                      fontWeight: 800,
                      display: 'inline',
                    }}
                  >
                    {t('pricing.cto.price')}
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'rgba(255, 255, 255, 0.7)',
                      display: 'inline',
                      ml: 1,
                    }}
                  >
                    {t('pricing.cto.period')}
                  </Typography>
                </Box>
                <Stack spacing={2} sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Strategic dashboard & KPIs
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Executive reporting suite
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Predictive analytics
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Multi-team insights
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Priority support
                    </Typography>
                  </Box>
                </Stack>
                <Button
                  variant='contained'
                  fullWidth
                  onClick={handleBookDemo}
                  sx={{
                    py: 2,
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 700,
                    fontSize: '1rem',
                    bgcolor: 'primary.main',
                    color: 'white',
                    boxShadow: '0 8px 32px rgba(33, 150, 243, 0.4)',
                    '&:hover': {
                      bgcolor: 'primary.dark',
                      boxShadow: '0 12px 40px rgba(33, 150, 243, 0.5)',
                    },
                  }}
                >
                  {t('pricing.buttons.startTrial')}
                </Button>
              </CardContent>
            </Card>

            {/* Enterprise Plan */}
            <Card
              sx={{
                maxWidth: 400,
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Typography variant='h5' sx={{ color: 'white', fontWeight: 700, mb: 1 }}>
                  {t('pricing.enterprise.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 4 }}>
                  {t('pricing.enterprise.description')}
                </Typography>
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant='h3'
                    sx={{
                      color: 'primary.light',
                      fontWeight: 800,
                      display: 'inline',
                    }}
                  >
                    {t('pricing.enterprise.price')}
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'rgba(255, 255, 255, 0.7)',
                      display: 'inline',
                      ml: 1,
                    }}
                  >
                    {t('pricing.enterprise.period')}
                  </Typography>
                </Box>
                <Stack spacing={2} sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Custom AI agents
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      White-label solution
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Dedicated success manager
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Custom integrations
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      24/7 premium support
                    </Typography>
                  </Box>
                </Stack>
                <Button
                  variant='outlined'
                  fullWidth
                  onClick={handleBookDemo}
                  sx={{
                    py: 2,
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: '1rem',
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    color: 'white',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: 'rgba(33, 150, 243, 0.1)',
                    },
                  }}
                >
                  {t('pricing.buttons.contactSales')}
                </Button>
              </CardContent>
            </Card>
          </Stack>
        </Container>
      </Box>

      {/* Footer */}
      <Box
        component='footer'
        sx={{
          py: 4,
          px: 3,
          mt: 'auto',
          borderTop: '1px solid rgba(255, 255, 255, 0.1)',
        }}
      >
        <Container maxWidth='lg'>
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent='space-between' alignItems='center' spacing={2}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <DynamicLogo height={24} width={24} />
              <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.6)' }}>
                {t('footer.copyright')}
              </Typography>
            </Box>
            <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.6)' }}>
              {t('footer.tagline')}
            </Typography>
          </Stack>
        </Container>
      </Box>

      {/* Lead Capture Modal */}
      <LeadCaptureModal open={leadModalOpen} onClose={handleLeadModalClose} />
    </Box>
  );
}
