'use client';

import { useRouter } from '@/i18n/navigation';
import { routing } from '@/i18n/routing';
import { zodResolver } from '@hookform/resolvers/zod';
import { useColorScheme } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import Stack from '@mui/material/Stack';
import { useTheme as useMuiTheme } from '@mui/material/styles';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { DesktopIcon } from '@phosphor-icons/react/dist/ssr/Desktop';
import { MoonIcon } from '@phosphor-icons/react/dist/ssr/Moon';
import { PaletteIcon } from '@phosphor-icons/react/dist/ssr/Palette';
import { SunIcon } from '@phosphor-icons/react/dist/ssr/Sun';
import { TranslateIcon } from '@phosphor-icons/react/dist/ssr/Translate';
import { Locale, useLocale, useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { z as zod } from 'zod';

// Import react-flagkit for flag components
const Flag = require('react-flagkit').default;

interface PreferencesStepProps {
  formData: {
    language: string;
    theme: string;
  };
  onChange: (_field: string, _value: string) => void;
}

type FormValues = {
  language: string;
  theme: string;
};

export const PreferencesStep = React.forwardRef<{ validate: () => Promise<boolean> }, PreferencesStepProps>(
  ({ formData, onChange }, ref): React.JSX.Element => {
    const t = useTranslations('onboarding');
    const tPreferences = useTranslations('settings.preferences');
    const router = useRouter();
    const params = useParams();
    const locale = useLocale();
    const muiTheme = useMuiTheme();
    const { mode, setMode } = useColorScheme();

    // Create schema with translations
    const schema = React.useMemo(() => {
      return zod.object({
        language: zod.string(),
        theme: zod.string(),
      });
    }, []);

    // Initialize form with React Hook Form
    const {
      control: _control,
      formState: { errors: _errors },
      setValue: _setValue,
      trigger: _trigger,
    } = useForm<FormValues>({
      defaultValues: formData,
      resolver: zodResolver(schema),
      mode: 'onBlur',
    });

    // Validate the form - this will be called from the parent component
    const validate = React.useCallback(async (): Promise<boolean> => {
      return true; // Preferences are optional, always valid
    }, []);

    // Expose the validate method to the parent component
    React.useImperativeHandle(ref, () => ({ validate }), [validate]);

    // Map of locale to country code for react-flagkit
    const localeToCountryCode: Record<string, string> = {
      'en-US': 'US',
      'pt-BR': 'BR',
      es: 'ES',
    };

    // Get language name from translation
    const getLanguageName = (localeStr: string): string => {
      return tPreferences('languageSwitch', { locale: localeStr.replaceAll('-', '_') });
    };

    // Handle language change
    const handleLanguageChange = (newLocale: string) => {
      onChange('language', newLocale);

      // Also update the UI language immediately
      if (newLocale !== locale) {
        // Store the current step in session storage before changing language
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('onboarding_step', '1'); // Preferences is step 1
        }

        router.replace(
          // @ts-expect-error -- TypeScript will validate that only known `params`
          // are used in combination with a given `pathname`. Since the two will
          // always match for the current route, we can skip runtime checks.
          { pathname: window.location.pathname, params },
          { locale: newLocale as Locale }
        );
        router.refresh();
      }
    };

    // Handle theme change
    const handleThemeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newTheme = event.target.value as 'light' | 'dark' | 'system';
      onChange('theme', newTheme);
      setMode(newTheme);
    };

    return (
      <Stack spacing={3}>
        <Typography variant='h5' component='h2'>
          {t('preferences.title')}
        </Typography>
        <Typography variant='body1' color='text.secondary'>
          {t('preferences.description')}
        </Typography>

        <Grid container spacing={4}>
          {/* Language Selector */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Stack spacing={2}>
              <Stack direction='row' spacing={1} alignItems='center'>
                <TranslateIcon fontSize='var(--icon-fontSize-md)' />
                <Typography variant='h6'>{tPreferences('language')}</Typography>
              </Stack>
              <Autocomplete
                id='language-select'
                options={routing.locales}
                value={formData.language}
                size='small'
                sx={{ maxWidth: 240 }}
                onChange={(_, newValue) => {
                  if (newValue) {
                    handleLanguageChange(newValue);
                  }
                }}
                getOptionLabel={(option) => {
                  if (typeof option === 'string') {
                    return getLanguageName(option);
                  }
                  return '';
                }}
                renderOption={(props, option) => {
                  // Extract key from props to pass it directly
                  const { key, ...otherProps } = props;
                  return (
                    <ListItem key={key} {...otherProps}>
                      <Box
                        component='span'
                        sx={{
                          minWidth: 36,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Flag country={localeToCountryCode[option]} size={20} />
                      </Box>
                      <ListItemText primary={getLanguageName(option)} />
                    </ListItem>
                  );
                }}
                renderInput={(inputParams) => (
                  <TextField
                    {...inputParams}
                    label={tPreferences('languageLabel')}
                    slotProps={{
                      input: {
                        ...inputParams.InputProps,
                        startAdornment: (
                          <>
                            <Box component='span' sx={{ ml: 1, mr: -0.5, display: 'flex', alignItems: 'center' }}>
                              <Flag country={localeToCountryCode[formData.language]} size={20} />
                            </Box>
                            {inputParams.InputProps.startAdornment}
                          </>
                        ),
                      },
                    }}
                  />
                )}
              />
            </Stack>
          </Grid>

          {/* Theme Selector */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Stack spacing={2}>
              <Stack direction='row' spacing={1} alignItems='center'>
                <PaletteIcon fontSize='var(--icon-fontSize-md)' />
                <Typography variant='h6'>{tPreferences('theme')}</Typography>
              </Stack>
              <FormControl component='fieldset' fullWidth>
                <RadioGroup
                  aria-labelledby='theme-selector-label'
                  name='theme-selector'
                  value={mode}
                  onChange={handleThemeChange}
                >
                  <FormControlLabel
                    value='system'
                    control={<Radio />}
                    label={
                      <Stack direction='row' spacing={1} alignItems='center'>
                        <DesktopIcon fontSize='var(--icon-fontSize-md)' color={muiTheme.palette.primary.main} />
                        <Typography>{tPreferences('themeOptions.system')}</Typography>
                      </Stack>
                    }
                  />
                  <FormControlLabel
                    value='light'
                    control={<Radio />}
                    label={
                      <Stack direction='row' spacing={1} alignItems='center'>
                        <SunIcon fontSize='var(--icon-fontSize-md)' color={muiTheme.palette.primary.main} />
                        <Typography>{tPreferences('themeOptions.light')}</Typography>
                      </Stack>
                    }
                  />
                  <FormControlLabel
                    value='dark'
                    control={<Radio />}
                    label={
                      <Stack direction='row' spacing={1} alignItems='center'>
                        <MoonIcon fontSize='var(--icon-fontSize-md)' color={muiTheme.palette.primary.main} />
                        <Typography>{tPreferences('themeOptions.dark')}</Typography>
                      </Stack>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </Stack>
          </Grid>
        </Grid>
      </Stack>
    );
  }
);
