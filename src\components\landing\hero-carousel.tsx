'use client';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import useMediaQuery from '@mui/material/useMediaQuery';
import { Lightning } from '@phosphor-icons/react/dist/ssr';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { DynamicLogo } from '@/components/core/logo';

interface HeroCarouselProps {
  onLeadModalOpen: () => void;
}

export function HeroCarousel({ onLeadModalOpen }: HeroCarouselProps): React.JSX.Element {
  const t = useTranslations('landing');
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));

  // Carousel state
  const [currentSlide, setCurrentSlide] = React.useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = React.useState(true);

  // Auto-advance slides
  React.useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % 2);
    }, 6000); // 6 seconds per slide

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  // Performance optimization: Memoize expensive calculations
  const heroFontSize = React.useMemo(() => {
    if (isMobile) return '2rem';
    if (isTablet) return '3rem';
    return '4.5rem';
  }, [isMobile, isTablet]);

  // iPhone mockup responsive sizing
  const iPhoneMockupSize = React.useMemo(() => {
    if (isMobile) {
      // For mobile screens, scale based on screen width
      return {
        width: { xs: 280, sm: 320 }, // 280px for very small screens, 320px for small screens
        height: { xs: 560, sm: 640 }, // Maintain 16:9 aspect ratio approximately
      };
    }
    if (isTablet) {
      return {
        width: 360,
        height: 720,
      };
    }
    // Desktop size
    return {
      width: 320,
      height: 640,
    };
  }, [isMobile, isTablet]);

  const handlePrevious = React.useCallback(() => {
    setIsAutoPlaying(false);
    setCurrentSlide((prev) => (prev - 1 + 2) % 2);
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000);
  }, []);

  const handleNext = React.useCallback(() => {
    setIsAutoPlaying(false);
    setCurrentSlide((prev) => (prev + 1) % 2);
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000);
  }, []);

  const handleDotClick = React.useCallback((index: number) => {
    setIsAutoPlaying(false);
    setCurrentSlide(index);
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000);
  }, []);

  // Slide content data
  const slides = [
    {
      id: 'ai-agents',
      title: t('aiAgents.title'),
      titleHighlight: t('aiAgents.titleHighlight'),
      subtitle: t('aiAgents.subtitle'),
      textAlign: { xs: 'center', md: 'center' },
      content: (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            position: 'relative',
          }}
        >
          <Box
            component='img'
            src='/assets/ai-agents.webp'
            alt='AI Agents Digital Crew'
            sx={{
              width: '100%',
              height: 'auto',
              borderRadius: '24px',
              transition: 'all 0.3s ease',
              boxShadow: '0 12px 40px rgba(0, 0, 0, 0.3)',
              '&:hover': {
                transform: 'translateY(-8px)',
              },
            }}
          />
        </Box>
      ),
    },
    {
      id: 'mobile-insights',
      title: t('hero.title'),
      titleHighlight: t('hero.titleHighlight'),
      subtitle: t('hero.subtitle'),
      textAlign: { xs: 'center', md: 'center' },
      content: (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          {/* iPhone 16 Notification Mockup */}
          <Box
            sx={{
              position: 'relative',
              width: iPhoneMockupSize.width,
              height: iPhoneMockupSize.height,
              mx: 'auto',
              // iPhone 16 frame styling
              bgcolor: '#1a1a1a', // Dark frame color
              borderRadius: '60px',
              padding: '0 8px',
              boxShadow: `
                0 0 0 2px #333,
                0 0 0 4px #1a1a1a,
                0 20px 40px rgba(0, 0, 0, 0.4),
                inset 0 0 0 1px rgba(255, 255, 255, 0.1)
              `,
              // iOS-style wallpaper background
              background: `
                radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.8) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(147, 51, 234, 0.6) 0%, transparent 50%),
                radial-gradient(circle at 20% 80%, rgba(236, 72, 153, 0.4) 0%, transparent 50%),
                linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)
              `,
              border: '5px solid black',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: '0px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '120px',
                height: '30px',
                bgcolor: '#1a1a1a',
                borderRadius: '0 0 20px 20px',
                zIndex: 10,
                // Dynamic Island styling
                boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.3)',
              },
              '&::after': {
                content: '""',
                position: 'absolute',
                top: '10px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '6px',
                height: '6px',
                bgcolor: '#333',
                borderRadius: '50%',
                zIndex: 11,
                // Camera dot
              },
            }}
          >
            {/* Screen area */}
            <Box
              sx={{
                width: '100%',
                height: '100%',
                borderRadius: '52px',
                overflow: 'hidden',
                position: 'relative',
                // Screen content area
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              {/* Status bar area */}
              <Box
                sx={{
                  height: '50px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  px: 3,
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: 600,
                  zIndex: 5,
                }}
              >
                <Box>9:41</Box>
                <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
                  {/* Signal bars */}
                  <Box sx={{ display: 'flex', gap: '2px', alignItems: 'end' }}>
                    <Box sx={{ width: '3px', height: '4px', bgcolor: 'white', borderRadius: '1px' }} />
                    <Box sx={{ width: '3px', height: '6px', bgcolor: 'white', borderRadius: '1px' }} />
                    <Box sx={{ width: '3px', height: '8px', bgcolor: 'white', borderRadius: '1px' }} />
                    <Box sx={{ width: '3px', height: '10px', bgcolor: 'white', borderRadius: '1px' }} />
                  </Box>
                </Box>
              </Box>

              {/* Content area with notification */}
              <Box
                sx={{
                  flex: 1,
                  position: 'relative',
                  overflow: 'hidden',
                  p: 2,
                  pt: 4,
                }}
              >
                {/* iOS Notification */}
                <Box
                  sx={{
                    bgcolor: 'rgba(40, 40, 40, 0.95)',
                    backdropFilter: 'blur(40px)',
                    borderRadius: '12px',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.4), 0 0 0 0.5px rgba(255, 255, 255, 0.05)',
                    overflow: 'hidden',
                    animation: 'iOSSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    '@keyframes iOSSlideIn': {
                      from: {
                        opacity: 0,
                        transform: 'translateY(-30px) scale(0.95)',
                      },
                      to: {
                        opacity: 1,
                        transform: 'translateY(0) scale(1)',
                      },
                    },
                  }}
                >
                  {/* Notification Header */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      px: 2,
                      py: 1.5,
                      borderBottom: '1px solid rgba(255, 255, 255, 0.08)',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                      <DynamicLogo height={20} width={20} />
                      <Typography
                        variant='caption'
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          fontWeight: 600,
                          fontSize: '0.75rem',
                        }}
                      >
                        BMS Pulse
                      </Typography>
                    </Box>
                    <Typography
                      variant='caption'
                      sx={{
                        color: 'rgba(255, 255, 255, 0.6)',
                        fontSize: '0.7rem',
                      }}
                    >
                      now
                    </Typography>
                  </Box>

                  {/* Notification Content */}
                  <Box sx={{ px: 2, py: 2 }}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: 2,
                      }}
                    >
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: '8px',
                          bgcolor: 'rgba(255, 69, 58, 0.15)',
                          border: '1px solid rgba(255, 69, 58, 0.3)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0,
                        }}
                      >
                        <Lightning size={20} color='#ff453a' />
                      </Box>
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography
                          variant='body2'
                          sx={{
                            color: 'rgba(255, 255, 255, 0.95)',
                            fontWeight: 500,
                            mb: 0.5,
                            fontSize: '0.875rem',
                            lineHeight: 1.3,
                          }}
                        >
                          {t('mockup.projectTitle')}
                        </Typography>
                        <Typography
                          variant='body2'
                          sx={{
                            color: 'rgba(255, 255, 255, 0.7)',
                            fontSize: '0.8rem',
                            lineHeight: 1.4,
                          }}
                        >
                          {t('mockup.projectSubtitle')}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      ),
    },
  ];

  return (
    <Container maxWidth={false} disableGutters sx={{ width: '100%', py: { xs: 1, sm: 2, md: 4 } }}>
      <Box sx={{ position: 'relative', minHeight: { xs: 'auto', md: '70vh' } }}>
        {/* Carousel Container */}
        <Box
          sx={{
            position: 'relative',
            overflow: 'hidden',
            borderRadius: 4,
          }}
        >
          {/* Slides Container */}
          <Box
            sx={{
              px: { xs: 1, sm: 2, md: 4 },
              display: 'flex',
              transform: `translateX(-${currentSlide * 100}%)`,
              transition: 'transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
              willChange: 'transform',
            }}
          >
            {slides.map((slide, index) => (
              <Box
                key={slide.id}
                sx={{
                  minWidth: '100%',
                  opacity: index === currentSlide ? 1 : 0.7,
                  transition: 'opacity 0.8s ease',
                }}
              >
                <Grid
                  container
                  spacing={{ xs: 3, sm: 4, md: 6 }}
                  alignItems='center'
                  sx={{ minHeight: { xs: 'auto', md: '70vh' } }}
                >
                  {/* Text Content */}
                  <Grid size={{ xs: 12, md: 6 }} order={{ xs: 2, md: index === 0 ? 1 : 2 }}>
                    <Stack spacing={{ xs: 3, sm: 4, md: 6 }} sx={{ textAlign: slide.textAlign }}>
                      <Typography
                        variant='h1'
                        sx={{
                          fontSize: heroFontSize,
                          fontWeight: 800,
                          lineHeight: { xs: 1.1, md: 1.05 },
                          color: 'white',
                          letterSpacing: '-0.02em',
                        }}
                      >
                        {slide.title}{' '}
                        <Box
                          component='span'
                          sx={{
                            background: (muiTheme) =>
                              `linear-gradient(135deg, ${muiTheme.palette.primary.light} 0%, ${muiTheme.palette.primary.main} 100%)`,
                            backgroundClip: 'text',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            display: 'inline-block',
                          }}
                        >
                          {slide.titleHighlight}
                        </Box>
                      </Typography>

                      <Typography
                        variant='h5'
                        sx={{
                          fontWeight: 400,
                          lineHeight: 1.6,
                          color: 'rgba(255, 255, 255, 0.85)',
                          fontSize: { xs: '1.25rem', md: '1.5rem' },
                        }}
                      >
                        {slide.subtitle}
                      </Typography>

                      {/* CTA Button - Only show on first slide */}
                      {index === 0 && (
                        <Box sx={{ display: 'flex', justifyContent: { xs: 'center', md: 'center' } }}>
                          <Button
                            size='large'
                            variant='contained'
                            onClick={onLeadModalOpen}
                            sx={{
                              py: { xs: 2.5, md: 2 }, // Larger touch target on mobile
                              px: { xs: 8, md: 6 }, // More padding on mobile
                              borderRadius: 4,
                              textTransform: 'none',
                              fontWeight: 700,
                              fontSize: { xs: '1rem', md: '1.125rem' },
                              bgcolor: 'primary.main',
                              color: 'white',
                              boxShadow: '0 12px 40px rgba(33, 150, 243, 0.4)',
                              minHeight: { xs: 48, md: 'auto' }, // Minimum touch target height
                              '&:hover': {
                                bgcolor: 'primary.dark',
                                boxShadow: '0 16px 48px rgba(33, 150, 243, 0.5)',
                                transform: 'translateY(-3px)',
                              },
                              '&:active': {
                                transform: 'translateY(-1px)',
                              },
                              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                              willChange: 'transform, box-shadow',
                            }}
                          >
                            {t('hero.ctaSecondary')}
                          </Button>
                        </Box>
                      )}
                    </Stack>
                  </Grid>

                  {/* Visual Content */}
                  <Grid size={{ xs: 12, md: 6 }} order={{ xs: 1, md: index === 0 ? 2 : 1 }}>
                    {slide.content}
                  </Grid>
                </Grid>
              </Box>
            ))}
          </Box>
        </Box>

        {/* Dot Indicators */}
        <Box
          sx={{
            position: 'absolute',
            bottom: { xs: 20, md: 32 },
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            gap: { xs: 3, md: 2 },
            zIndex: 10,
          }}
        >
          {slides.map((_, index) => (
            <Box
              key={index}
              onClick={() => handleDotClick(index)}
              sx={{
                // Larger touch targets for mobile
                width: { xs: 16, md: 12 },
                height: { xs: 16, md: 12 },
                borderRadius: '50%',
                bgcolor: index === currentSlide ? 'primary.main' : 'rgba(255, 255, 255, 0.4)',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                border: '2px solid rgba(255, 255, 255, 0.2)',
                // Larger touch area for better mobile interaction
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: { xs: 44, md: 32 }, // 44px minimum touch target for mobile
                  height: { xs: 44, md: 32 },
                  borderRadius: '50%',
                  // Invisible but clickable area
                },
                '&:hover': {
                  bgcolor: index === currentSlide ? 'primary.light' : 'rgba(255, 255, 255, 0.6)',
                  transform: 'scale(1.2)',
                },
                '&:active': {
                  transform: 'scale(0.9)',
                },
              }}
            />
          ))}
        </Box>
      </Box>
    </Container>
  );
}
